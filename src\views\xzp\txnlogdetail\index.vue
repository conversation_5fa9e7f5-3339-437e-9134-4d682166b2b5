<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" :rules="rules" ref="searchForm" class="search-form" label-width="100px" size="small">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="起始日期" prop="startTime">
              <el-date-picker
                style="width: 200px;"
                v-model="search.startTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择起始日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终止日期" prop="endTime">
              <el-date-picker
                style="width: 200px;"
                v-model="search.endTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择终止日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            <el-button type="success" icon="el-icon-download" @click="handleExport" :disabled="!data.length">导出</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据为空时的提示信息 -->
    <el-alert
      v-if="!data.length && !loading && !hasSearched"
      title="请设置查询条件后点击查询按钮获取数据"
      type="info"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
    />

    <!-- 数据统计信息 -->
    <div class="data-summary" v-if="hasSearched && data.length > 0">
      <span class="summary-text">共查询到 {{ data.length }} 条记录</span>
    </div>

    <!-- 查询结果表格 -->
    <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
    />
  </div>
</template>

<script>
import { queryTxnLogDetail, exportTxnLogDetail } from '@/api/xzp/inttxnlog'
import { formatDate } from '@/utils'

export default {
  name: 'TxnLogDetail',
  data() {
    return {
      loading: false,
      hasSearched: false,
      data: [],
      formParent: {},
      search: {
        startTime: '',
        endTime: ''
      },
      rules: {
        startTime: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择终止日期', trigger: 'change' }
        ]
      },

      option: {
        index: true,
        indexLabel: '序号',
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        card: true,
        align: 'center',
        menuAlign: 'center',
        searchBtn: false,
        refreshBtn: false,
        emptyBtn: false,
        tip: false,
        columnBtn: false,
        menu: false,
        page: false, // 完全禁用分页功能
        showSummary: true,
        summaryMethod: this.getSummaries,
        viewTitle: '缴费明细查询',
        rowClassName: this.getRowClassName,
        column: [
          {
            prop: 'setdate',
            label: '银联清算日期',
            width: 120
          },
          {
            prop: 'str',
            label: '业务代码',
            width: 200,
            formatter: (row, column, cellValue) => {
              // 如果是总计行，在业务代码列显示"总计"
              if (cellValue === '总计') {
                return '总计'
              }
              // 如果是小计行，在业务代码列显示"小计"
              if (cellValue === '小计') {
                return '小计'
              }
              return cellValue
            }
          },
          {
            prop: 'merchid',
            label: '委托代码',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 总计行和小计行不显示委托代码
              if (row.str === '总计' || row.str === '小计') {
                return ''
              }
              return cellValue
            }
          },
          {
            prop: 'str30',
            label: '子业务代码',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 总计行和小计行不显示子业务代码
              if (row.str === '总计' || row.str === '小计') {
                return ''
              }
              return cellValue || ''
            }
          },
          {
            prop: 'str31',
            label: '子业务',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 总计行和小计行不显示子业务名称
              if (row.str === '总计' || row.str === '小计') {
                return ''
              }
              return cellValue
            }
          },
          {
            prop: 'num',
            label: '应付笔数',
            width: 100,
            formatter: (row, column, cellValue) => {
              return cellValue ? Number(cellValue).toLocaleString() : '0'
            }
          },
          {
            prop: 'amt',
            label: '应付金额(元)',
            width: 150,
            formatter: (row, column, cellValue) => {
              return cellValue ? (Number(cellValue) / 100).toFixed(2) : '0.00'
            }
          },
          {
            prop: 'receivableNum',
            label: '应收笔数',
            width: 100,
            formatter: (row, column, cellValue) => {
              return cellValue ? Number(cellValue).toLocaleString() : '0'
            }
          },
          {
            prop: 'receivableAmt',
            label: '应收金额(元)',
            width: 150,
            formatter: (row, column, cellValue) => {
              return '0.00'
            }
          },
          {
            prop: 'netAmt',
            label: '轧差金额(元)',
            width: 150,
            formatter: (row, column, cellValue, index) => {
              const receivable = 0.00
              const payable = row.amt ? (Number(row.amt) / 100) : 0
              const net = receivable - payable
              return net.toFixed(2)
            }
          }
        ]
      }
    }
  },
  methods: {
    // yo-table加载数据方法
    onLoad() {
      this.getList()
    },
    
    // yo-table搜索变化方法
    searchChange(params, done) {
      this.search = { ...this.search, ...params }
      this.getList()
      done()
    },
    
    // yo-table刷新方法
    refresh() {
      this.getList()
    },

    // 设置行样式类名
    getRowClassName({ row, rowIndex }) {
      // 如果是总计行，添加总计样式
      if (row.str === '总计' || row.is_subtotal === 2) {
        return 'total-row'
      }
      // 如果是小计行，添加特殊样式
      if (row.str === '小计' || row.is_subtotal === 1) {
        return 'subtotal-row'
      }
      return ''
    },

    // 处理返回的数据
    processData(data) {
      if (!Array.isArray(data)) {
        return []
      }

      // 对数据进行处理，确保小计行和总计行的正确显示
      return data.map(item => {
        // 如果是总计行，确保相关字段为空
        if (item.str === '总计' || item.is_subtotal === 2) {
          return {
            ...item,
            str30: '', // 子业务代码为空
            merchid: '', // 委托代码为空
            opecd: '', // 业务代码为空
            str31: '' // 子业务名称为空
          }
        }
        // 如果是小计行，确保相关字段为空
        if (item.str === '小计' || item.is_subtotal === 1) {
          return {
            ...item,
            str30: '', // 子业务代码为空
            merchid: '', // 委托代码为空
            opecd: '' // 业务代码为空（除了str字段显示"小计"）
          }
        }
        // 非小计行，保持原有数据，确保str30字段正常显示
        return {
          ...item,
          str30: item.str30 || '' // 确保子业务代码字段存在
        }
      })
    },

    // 计算总计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        
        const values = data.map(item => Number(item[column.property]))
        
        if (column.property === 'num') {
          // 应付笔数总计
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + value
            } else {
              return prev
            }
          }, 0)
          sums[index] = sum.toLocaleString()
        } else if (column.property === 'amt') {
          // 应付金额总计
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + value
            } else {
              return prev
            }
          }, 0)
          sums[index] = (sum / 100).toFixed(2)
        } else if (column.property === 'receivableAmt') {
          // 应收金额总计（固定为0.00）
          sums[index] = '0.00'
        } else if (column.property === 'netAmt') {
          // 轧差金额总计（应收总计 - 应付总计）
          const payableSum = data.reduce((prev, curr) => {
            const value = Number(curr.amt)
            if (!isNaN(value)) {
              return prev + value
            } else {
              return prev
            }
          }, 0)
          const receivableSum = 0 // 应收金额固定为0
          const netSum = receivableSum - (payableSum / 100)
          sums[index] = netSum.toFixed(2)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    
    // 查询数据
    handleQuery() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.getList()
        }
      })
    },
    
    // 获取列表数据
    async getList() {
      this.loading = true
      this.hasSearched = true
      try {
        const response = await queryTxnLogDetail(this.search)
        console.log('API响应数据:', response)
        if (response.code === '0') {
          // 检查数据结构
          if (response.data && response.data.list) {
            this.data = this.processData(response.data.list)
          } else if (Array.isArray(response.data)) {
            // 如果直接返回数组
            this.data = this.processData(response.data)
          } else {
            console.warn('数据结构异常:', response.data)
            this.data = []
          }
          console.log('处理后的数据:', this.data)
        } else {
          this.$message.error(response.message || '查询失败')
          this.data = []
        }
      } catch (error) {
        console.error('查询缴费明细失败:', error)
        this.$message.error('查询失败，请稍后重试')
        this.data = []
      } finally {
        this.loading = false
      }
    },
    
    // 重置查询
    resetQuery() {
      this.$refs.searchForm.resetFields()
      this.search = {
        startTime: '',
        endTime: ''
      }
      this.data = []
      this.hasSearched = false
    },
    
    // 日期范围验证
    validateDateRange() {
      if (this.search.startTime && this.search.endTime) {
        const start = new Date(this.search.startTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))
        const end = new Date(this.search.endTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))

        if (start > end) {
          this.$message.warning('起始日期不能大于终止日期')
          this.search.endTime = ''
          return
        }
      }
    },
    
    // 导出功能
    handleExport() {
      if (!this.data.length) {
        this.$message.warning('没有可导出的数据')
        return
      }
      
      this.$confirm('确认导出缴费明细数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportData()
      })
    },
    
    // 执行导出
    async exportData() {
      try {
        const params = {
          ...this.search
        }

        // 调用后端导出接口
        const response = await exportTxnLogDetail(params)
        this.handleExportData(response)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    },

    // 处理返回的流文件
    handleExportData(res) {
      if (!res) return
      let data = res.data
      let filename = '缴费明细列表.xls'

      // 尝试从响应头获取文件名
      if (res.headers && res.headers['content-disposition']) {
        const disposition = res.headers['content-disposition']
        const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURI(filenameMatch[1].replace(/['"]/g, ''))
        }
      } else {
        // 如果没有从响应头获取到文件名，使用默认格式
        filename = `缴费明细_${this.search.startTime}_${this.search.endTime}_${formatDate(new Date(), 'yyyyMMddHHmmss')}.xls`
      }

      const link = document.createElement('a')
      // 创建 Blob对象 存储二进制文件
      let blob = new Blob([data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success('导出成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  
  .el-form {
    .el-row {
      margin-bottom: 10px;
    }
    
    .search-btns {
      text-align: center;
      margin-top: 10px;
    }
  }
}

.custom-input {
  width: 100%;
}

.total-info {
  float: right;
  color: #909399;
  font-size: 14px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

/* 数据统计信息样式 */
.data-summary {
  margin-bottom: 16px;
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.summary-text {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

/* 小计行样式 */
::v-deep .subtotal-row {
  background-color: #f5f7fa !important;
  font-weight: bold;
  border-top: 2px solid #409eff;
  border-bottom: 1px solid #dcdfe6;
}

::v-deep .subtotal-row td {
  background-color: #f5f7fa !important;
  color: #303133;
}

/* 小计行中的"小计"文字特殊样式 */
::v-deep .subtotal-row .cell {
  font-weight: bold;
  color: #409eff;
}

/* 总计行样式 */
::v-deep .total-row {
  background-color: #e6f7ff !important;
  font-weight: bold;
  border-top: 3px solid #1890ff;
  border-bottom: 2px solid #1890ff;
}

::v-deep .total-row td {
  background-color: #e6f7ff !important;
  color: #1890ff;
  font-weight: bold;
}

/* 总计行中的"总计"文字特殊样式 */
::v-deep .total-row .cell {
  font-weight: bold;
  color: #1890ff;
  font-size: 16px;
}
</style>